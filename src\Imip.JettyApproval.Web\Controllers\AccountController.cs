using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using System.Threading.Tasks;

namespace Imip.JettyApproval.Web.Controllers;

[Route("[controller]/[action]")]
public class AccountController : Controller
{
    private readonly ILogger<AccountController> _logger;

    public AccountController(ILogger<AccountController> logger)
    {
        _logger = logger;
    }

    [HttpGet]
    public IActionResult ExternalLogin(string provider, string? returnUrl = null)
    {
        _logger.LogInformation("External login requested for provider: {Provider}, returnUrl: {ReturnUrl}", provider, returnUrl);

        // Request a redirect to the external login provider
        var redirectUrl = Url.Action(nameof(ExternalLoginCallback), "Account", new { returnUrl });
        var properties = new AuthenticationProperties { RedirectUri = redirectUrl };

        return Challenge(properties, provider);
    }

    [HttpGet]
    public async Task<IActionResult> ExternalLoginCallback(string? returnUrl = null, string? remoteError = null)
    {
        _logger.LogInformation("External login callback received. ReturnUrl: {ReturnUrl}, RemoteError: {RemoteError}", returnUrl, remoteError);

        if (remoteError != null)
        {
            _logger.LogWarning("Error from external provider: {RemoteError}", remoteError);
            TempData["ErrorMessage"] = $"Error from external provider: {remoteError}";
            return RedirectToPage("/Account/Login");
        }

        // The user should be authenticated at this point
        if (User.Identity?.IsAuthenticated == true)
        {
            _logger.LogInformation("User successfully authenticated via external provider");

            // Redirect to the return URL or default page
            if (!string.IsNullOrEmpty(returnUrl) && Url.IsLocalUrl(returnUrl))
            {
                _logger.LogInformation("Redirecting to return URL: {ReturnUrl}", returnUrl);
                return Redirect(returnUrl);
            }

            // Default redirect to admin page for this application
            _logger.LogInformation("Redirecting to default admin page");
            return RedirectToAction("Index", "Admin");
        }

        // If we got this far, something failed
        _logger.LogWarning("Authentication failed - user not authenticated after external login callback");
        TempData["ErrorMessage"] = "Unable to authenticate with external provider.";
        return RedirectToPage("/Account/Login");
    }

    [HttpPost]
    [ValidateAntiForgeryToken]
    public async Task<IActionResult> Logout()
    {
        _logger.LogInformation("User logout requested");

        // Sign out from both cookie and OIDC schemes
        await HttpContext.SignOutAsync("Cookies");
        await HttpContext.SignOutAsync("oidc");

        return RedirectToPage("/Account/Login");
    }
}
