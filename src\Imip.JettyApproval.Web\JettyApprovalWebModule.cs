using Imip.JettyApproval.EntityFrameworkCore;
using Imip.JettyApproval.Localization;
using Imip.JettyApproval.MultiTenancy;
using Imip.JettyApproval.Web.Authorization.Users;
using Imip.JettyApproval.Web.Menus;
using Imip.JettyApproval.Web.Modules;
using Imip.JettyApproval.Web.Services.Attachments;
using Imip.JettyApproval.Web.Services.Interfaces;
using Imip.JettyApproval.Web.Swagger;
using InertiaCore.Extensions;
using Microsoft.AspNetCore.Antiforgery;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Extensions.DependencyInjection;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Account.Web;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc.AntiForgery;
using Volo.Abp.AspNetCore.Mvc.Localization;
using Volo.Abp.AspNetCore.Mvc.UI.Bundling;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite.Bundling;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.Toolbars;
using Volo.Abp.AspNetCore.Serilog;
using Volo.Abp.Autofac;
using Volo.Abp.AutoMapper;
using Volo.Abp.FeatureManagement;
using Volo.Abp.Identity.Web;
using Volo.Abp.Modularity;
using Volo.Abp.OpenIddict;
using Volo.Abp.PermissionManagement;
using Volo.Abp.Security.Claims;
using Volo.Abp.Studio.Client.AspNetCore;
using Volo.Abp.Swashbuckle;
using Volo.Abp.TenantManagement.Web;
using Volo.Abp.UI.Navigation;
using Volo.Abp.UI.Navigation.Urls;
using Volo.Abp.VirtualFileSystem;

namespace Imip.JettyApproval.Web;

[DependsOn(
    typeof(JettyApprovalHttpApiModule),
    typeof(JettyApprovalApplicationModule),
    typeof(JettyApprovalEntityFrameworkCoreModule),
    typeof(AbpAspNetCoreMvcModule),
    typeof(AbpAutofacModule),
    typeof(AbpStudioClientAspNetCoreModule),
    typeof(AbpIdentityWebModule),
    typeof(AbpAspNetCoreMvcUiLeptonXLiteThemeModule),
    typeof(AbpAccountWebOpenIddictModule),
    typeof(AbpTenantManagementWebModule),
    typeof(AbpFeatureManagementWebModule),
    typeof(AbpSwashbuckleModule),
    typeof(AbpAspNetCoreSerilogModule),
    typeof(PermissionCheckerModule)
)]
public class JettyApprovalWebModule : AbpModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        var hostingEnvironment = context.Services.GetHostingEnvironment();
        var configuration = context.Services.GetConfiguration();

        context.Services.PreConfigure<AbpMvcDataAnnotationsLocalizationOptions>(options =>
        {
            options.AddAssemblyResource(
                typeof(JettyApprovalResource),
                typeof(JettyApprovalDomainModule).Assembly,
                typeof(JettyApprovalDomainSharedModule).Assembly,
                typeof(JettyApprovalApplicationModule).Assembly,
                typeof(JettyApprovalApplicationContractsModule).Assembly,
                typeof(JettyApprovalWebModule).Assembly
            );
        });

        // Remove OpenIddict validation since we're using external SSO only
        // The external identity server handles token validation
        // We only need OpenIdConnect for authentication flow

        // Disable OpenIddict server configuration since we're using external SSO
        // if (!hostingEnvironment.IsDevelopment())
        // {
        //     PreConfigure<AbpOpenIddictAspNetCoreOptions>(options =>
        //     {
        //         options.AddDevelopmentEncryptionAndSigningCertificate = false;
        //     });

        //     PreConfigure<OpenIddictServerBuilder>(serverBuilder =>
        //     {
        //         serverBuilder.AddProductionEncryptionAndSigningCertificate("openiddict.pfx", configuration["AuthServer:CertificatePassPhrase"]!);
        //         serverBuilder.SetIssuer(new Uri(configuration["AuthServer:Authority"]!));
        //     });
        // }
    }

    private void ConfigureAntiforgery(ServiceConfigurationContext context, IConfiguration configuration, IWebHostEnvironment hostingEnvironment)
    {
        // Configure ABP antiforgery options
        Configure<AbpAntiForgeryOptions>(options =>
        {
            options.TokenCookie.Expiration = TimeSpan.FromDays(365);
            // In development, allow non-HTTPS for easier debugging
            if (hostingEnvironment.IsDevelopment())
            {
                options.TokenCookie.SecurePolicy = CookieSecurePolicy.SameAsRequest;
            }
        });

        // Configure ASP.NET Core antiforgery options
        context.Services.Configure<AntiforgeryOptions>(options =>
        {
            options.Cookie.Name = ".Imip.JettyApproval.Antiforgery";
            options.Cookie.HttpOnly = true;

            var requireHttps = !hostingEnvironment.IsDevelopment() &&
                              configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata", true);

            options.Cookie.SecurePolicy = requireHttps ? CookieSecurePolicy.Always : CookieSecurePolicy.SameAsRequest;
            options.Cookie.SameSite = SameSiteMode.Lax;
            options.Cookie.MaxAge = TimeSpan.FromHours(2); // Longer lifetime for development
            options.FormFieldName = "__RequestVerificationToken";
            options.HeaderName = "X-CSRF-TOKEN";
        });
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        var hostingEnvironment = context.Services.GetHostingEnvironment();
        var configuration = context.Services.GetConfiguration();

        // Add this configuration for handling larger headers
        //context.Services.Configure<IISServerOptions>(static options =>
        //{
        //    options.MaxRequestHeadersTotalSize = 65536; // Increased header size limit (64KB)
        //});

        // For Kestrel
        context.Services.Configure<KestrelServerOptions>(options =>
        {
            options.Limits.MaxRequestHeadersTotalSize = 65536; // Increased header size limit (64KB)
        });

        if (!configuration.GetValue<bool>("App:DisablePII"))
        {
            Microsoft.IdentityModel.Logging.IdentityModelEventSource.ShowPII = true;
            Microsoft.IdentityModel.Logging.IdentityModelEventSource.LogCompleteSecurityArtifact = true;
        }

        // Configure forwarded headers for proxy scenarios
        Configure<ForwardedHeadersOptions>(options =>
        {
            options.ForwardedHeaders = ForwardedHeaders.XForwardedProto;
        });

        ConfigureAntiforgery(context, configuration, hostingEnvironment);

        context.Services.AddInertia(options =>
        {
            options.RootView = "~/Views/App.cshtml";
        });

        context.Services.AddViteHelper(options =>
        {
            options.PublicDirectory = "wwwroot";
            options.ManifestFilename = "manifest.json";
            options.BuildDirectory = "build";
        });

        ConfigureBundles();
        ConfigureUrls(configuration);
        ConfigureHealthChecks(context);
        ConfigureAuthentication(context, configuration);
        ConfigureAutoMapper();
        ConfigureVirtualFileSystem(hostingEnvironment);
        ConfigureNavigationServices();
        ConfigureAutoApiControllers();
        ConfigureSwaggerServices(context.Services);

        Configure<PermissionManagementOptions>(options =>
        {
            options.IsDynamicPermissionStoreEnabled = true;
        });

        // context.Services.AddMemoryCache();
        // context.Services.AddScoped<ITokenStore, InMemoryTokenStore>();

        // Register the authorization handler
        //context.Services.AddScoped<IAuthorizationHandler, DynamicPolicyAuthorizationHandler>();

        // Register the HttpContextAccessor if it's not already registered
        context.Services.AddHttpContextAccessor();

        // Register HttpClientFactory for testing and other HTTP operations
        context.Services.AddHttpClient();

        // Configure session for deferred user synchronization
        context.Services.AddSession(options =>
        {
            options.IdleTimeout = TimeSpan.FromMinutes(30);
            options.Cookie.HttpOnly = true;
            options.Cookie.IsEssential = true;
            options.Cookie.Name = ".Imip.JettyApproval.Session";
        });

        // Register the ApplicationConfigurationService
        //context.Services.AddScoped<ApplicationConfigurationService>();

        // Register an HTTP client for Identity Server communication
        //context.Services.AddHttpClient("IdentityServer", client =>
        //{
        //    var configuration = context.Services.GetConfiguration();
        //    client.BaseAddress = new Uri(configuration["AuthServer:Authority"] ?? "https://api-identity-dev.imip.co.id");
        //});

        // Register JWT token caching service
        //context.Services.AddSingleton<IJwtTokenCacheService, JwtTokenCacheService>();

        // Configure memory cache for performance optimization
        //context.Services.AddMemoryCache(options =>
        //{
        //    options.SizeLimit = 10000; // Limit cache size
        //    options.CompactionPercentage = 0.25; // Compact when 75% full
        //});

        // Replace permission checker with cached version for better performance
        //context.Services.Replace(ServiceDescriptor.Transient<IPermissionChecker, CachedPermissionChecker>());

        // Register user synchronization service
        context.Services.AddTransient<IUserSynchronizationService, UserSynchronizationService>();

        // Configure user synchronization options
        context.Services.Configure<UserSynchronizationOptions>(options =>
        {
            options.IsEnabled = true;
            options.UpdateExistingUsers = true;
            options.SynchronizeRoles = true;
            options.SynchronizeClaims = true;
            options.EnableLogging = true;
            options.MaxRetryAttempts = 3;
            options.RetryDelayMilliseconds = 1000;
        });
    }

    private void ConfigureHealthChecks(ServiceConfigurationContext context)
    {
        context.Services.AddHealthChecks();
    }

    private void ConfigureBundles()
    {
        Configure<AbpBundlingOptions>(options =>
        {
            options.StyleBundles.Configure(
                LeptonXLiteThemeBundles.Styles.Global,
                bundle =>
                {
                    bundle.AddFiles("/global-styles.css");
                }
            );

            options.ScriptBundles.Configure(
                LeptonXLiteThemeBundles.Scripts.Global,
                bundle =>
                {
                    bundle.AddFiles("/global-scripts.js");
                }
            );
        });
    }

    private void ConfigureUrls(IConfiguration configuration)
    {
        Configure<AppUrlOptions>(options =>
        {
            options.Applications["MVC"].RootUrl = configuration["App:SelfUrl"];
        });
    }

    private void ConfigureAuthentication(ServiceConfigurationContext context, IConfiguration configuration)
    {
        context.Services.AddAuthentication(options =>
        {
            options.DefaultScheme = "Cookies";
            options.DefaultChallengeScheme = "oidc";
            options.DefaultSignOutScheme = "oidc";
        })
        .AddCookie("Cookies", options =>
        {
            options.ExpireTimeSpan = TimeSpan.FromDays(365);
            options.SlidingExpiration = true;
            options.Cookie.Name = ".Imip.JettyApproval.Cookies";
            options.Cookie.HttpOnly = true;
            options.Cookie.IsEssential = true;

            var hostingEnvironment = context.Services.GetHostingEnvironment();
            var isDevelopment = hostingEnvironment.IsDevelopment();

            // Configure cookie settings based on HTTPS usage
            var useHttps = configuration.GetValue<bool>("App:UseHttps", false);
            if (useHttps)
            {
                options.Cookie.SecurePolicy = CookieSecurePolicy.Always;
                options.Cookie.SameSite = SameSiteMode.None;
            }
            else
            {
                options.Cookie.SecurePolicy = CookieSecurePolicy.SameAsRequest;
                options.Cookie.SameSite = SameSiteMode.Lax;
            }
            options.LoginPath = "/Account/Login";
            options.LogoutPath = "/Account/Logout";
            options.AccessDeniedPath = "/Account/AccessDenied";
        })
        .AddOpenIdConnect("oidc", options =>
        {
            // Basic OIDC configuration
            options.Authority = configuration["OpenIdConnect:Authority"];
            options.RequireHttpsMetadata = configuration.GetValue<bool>("OpenIdConnect:RequireHttpsMetadata", false);

            options.ClientId = configuration["OpenIdConnect:ClientId"];
            options.ClientSecret = configuration["OpenIdConnect:ClientSecret"];

            // Use authorization code flow with PKCE
            options.ResponseType = "code";
            options.UsePkce = true;

            // Basic scopes
            options.Scope.Clear();
            options.Scope.Add("openid");
            options.Scope.Add("profile");
            options.Scope.Add("email");

            // Save tokens and get claims from UserInfo
            options.SaveTokens = true;
            options.GetClaimsFromUserInfoEndpoint = true;

            // Callback paths
            options.CallbackPath = "/signin-oidc";
            options.SignedOutCallbackPath = "/signout-callback-oidc";

            // Enhanced event handlers for better SSO integration
            options.Events = new OpenIdConnectEvents
            {
                OnTokenValidated = async context =>
                {
                    // Store user claims for synchronization
                    if (context.Principal?.Claims != null)
                    {
                        var claimsData = context.Principal.Claims
                            .Where(c => !string.IsNullOrEmpty(c.Type) && !string.IsNullOrEmpty(c.Value))
                            .ToDictionary(c => c.Type, c => c.Value);

                        context.HttpContext.Session.SetString("PendingUserSync",
                            System.Text.Json.JsonSerializer.Serialize(claimsData));
                    }
                },

                OnAuthenticationFailed = context =>
                {
                    context.Response.Redirect("/Account/Login?error=authentication_failed");
                    context.HandleResponse();
                    return Task.CompletedTask;
                },

                OnRemoteFailure = context =>
                {
                    context.Response.Redirect("/Account/Login?error=sso_failed");
                    context.HandleResponse();
                    return Task.CompletedTask;
                },

                OnRedirectToIdentityProviderForSignOut = context =>
                {
                    var postLogoutRedirectUri = configuration["OpenIdConnect:PostLogoutRedirectUri"] ??
                                              configuration["App:SelfUrl"];
                    context.ProtocolMessage.PostLogoutRedirectUri = postLogoutRedirectUri;
                    return Task.CompletedTask;
                },

                OnTicketReceived = context =>
                {
                    // This ensures the authentication ticket is properly processed
                    return Task.CompletedTask;
                }
            };

        });

        context.Services.Configure<AbpClaimsPrincipalFactoryOptions>(options =>
        {
            options.IsDynamicClaimsEnabled = true;
        });
    }

    private void ConfigureAutoMapper()
    {
        Configure<AbpAutoMapperOptions>(options =>
        {
            options.AddMaps<JettyApprovalWebModule>();
        });
    }

    private static bool IsRunningInContainer()
    {
        // Check for container-specific environment indicators
        return File.Exists("/.dockerenv") ||
               (File.Exists("/proc/1/cgroup") && File.ReadAllText("/proc/1/cgroup").Contains("/docker/"));
    }

    private void ConfigureVirtualFileSystem(IWebHostEnvironment hostingEnvironment)
    {
        Configure<AbpVirtualFileSystemOptions>(options =>
        {
            options.FileSets.AddEmbedded<JettyApprovalWebModule>();

            // Only replace embedded resources with physical files in a development environment,
            // and only if we're not running in a container
            if (hostingEnvironment.IsDevelopment() && !IsRunningInContainer())
            {
                try
                {
                    var domainSharedPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.JettyApproval.Domain.Shared", Path.DirectorySeparatorChar));
                    if (Directory.Exists(domainSharedPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<JettyApprovalDomainSharedModule>(domainSharedPath);
                    }

                    var domainPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.JettyApproval.Domain", Path.DirectorySeparatorChar));
                    if (Directory.Exists(domainPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<JettyApprovalDomainModule>(domainPath);
                    }

                    var contractsPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.JettyApproval.Application.Contracts", Path.DirectorySeparatorChar));
                    if (Directory.Exists(contractsPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<JettyApprovalApplicationContractsModule>(
                            contractsPath);
                    }

                    var appPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.JettyApproval.Application", Path.DirectorySeparatorChar));
                    if (Directory.Exists(appPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<JettyApprovalApplicationModule>(appPath);
                    }

                    var httpApiPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}..{0}src{0}Imip.JettyApproval.HttpApi", Path.DirectorySeparatorChar));
                    if (Directory.Exists(httpApiPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<JettyApprovalHttpApiModule>(httpApiPath);
                    }

                    if (Directory.Exists(hostingEnvironment.ContentRootPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<JettyApprovalWebModule>(hostingEnvironment
                            .ContentRootPath);
                    }
                }
                catch (Exception ex)
                {
                    // Log the exception but continue without replacing embedded resources
                    Console.WriteLine($"Error configuring virtual file system: {ex.Message}");
                }
            }
        });
    }

    private void ConfigureNavigationServices()
    {
        Configure<AbpNavigationOptions>(options =>
        {
            options.MenuContributors.Add(new JettyApprovalMenuContributor());
        });

        Configure<AbpToolbarOptions>(options =>
        {
            options.Contributors.Add(new JettyApprovalToolbarContributor());
        });
    }

    private void ConfigureAutoApiControllers()
    {
        Configure<AbpAspNetCoreMvcOptions>(options =>
        {
            options.ConventionalControllers.Create(typeof(JettyApprovalApplicationModule).Assembly);
        });
    }

    private void ConfigureSwaggerServices(IServiceCollection services)
    {
        services.AddAbpSwaggerGen(
            options =>
            {
                options.SwaggerDoc("v1", new OpenApiInfo { Title = "JettyApproval API", Version = "v1" });
                // Configure Swagger to handle file uploads with IFormFile
                options.OperationFilter<SwaggerFileOperationFilter>();

                // Add support for multipart/form-data
                options.MapType<IFormFile>(() => new OpenApiSchema
                {
                    Type = "string",
                    Format = "binary"
                });

                // Add support for FileUploadFormDto
                options.MapType<FileUploadFormDto>(() => new OpenApiSchema
                {
                    Type = "object",
                    Properties = new Dictionary<string, OpenApiSchema>
                    {
                        ["File"] = new OpenApiSchema { Type = "string", Format = "binary" },
                        ["Description"] = new OpenApiSchema { Type = "string" },
                        ["ReferenceId"] = new OpenApiSchema { Type = "string", Format = "uuid" },
                        ["ReferenceType"] = new OpenApiSchema { Type = "string" }
                    },
                    Required = new HashSet<string> { "File" }
                });

                options.DocInclusionPredicate((docName, description) =>
                {
                    // Check if the controller name contains "Abp" (for ABP framework controllers)
                    if (description.ActionDescriptor != null &&
                        description.ActionDescriptor.RouteValues != null &&
                        description.ActionDescriptor.RouteValues.TryGetValue("controller", out var controllerName) &&
                        controllerName != null &&
                        (controllerName.Contains("Abp") ||
                         controllerName.StartsWith("Abp")))
                    {
                        if (description.RelativePath != null &&
                            (description.RelativePath.Contains("application-configuration") ||
                             description.RelativePath.Contains("api/abp/application-configuration")))
                        {
                            return true; // Exclude application-configuration API
                        }

                        if (description.RelativePath != null &&
                        (description.RelativePath.Contains("/tenant") ||
                         description.RelativePath.Contains("/tenants")))
                        {
                            return true; // Exclude tenant-related APIs
                        }

                        // Check if the relative path contains "account" or "accounts"
                        if (description.RelativePath != null &&
                            (description.RelativePath.Contains("/account") ||
                             description.RelativePath.Contains("/accounts")))
                        {
                            return true; // Exclude account-related APIs
                        }

                        // Exclude ABP framework controllers
                        return false;
                    }


                    // Check if the API is from the Identity module
                    if (description.GroupName != null && description.GroupName.Contains("Identity"))
                    {
                        return false; // Exclude identity management APIs
                    }

                    if (description.GroupName != null && description.GroupName.Contains("PermissionManagement"))
                    {
                        return false; // Exclude permission management APIs
                    }

                    if (description.GroupName != null && description.GroupName.Contains("FeatureManagement"))
                    {
                        return false; // Exclude feature management APIs
                    }

                    if (description.GroupName != null && description.GroupName.Contains("SettingManagement"))
                    {
                        return false; // Exclude feature management APIs
                    }

                    if (description.RelativePath != null &&
                        (description.RelativePath.Contains("/permission") ||
                         description.RelativePath.Contains("/permissions")))
                    {
                        return false; // Exclude account-related APIs
                    }

                    if (description.RelativePath != null &&
                        (description.RelativePath.Contains("/feature") ||
                         description.RelativePath.Contains("/features")))
                    {
                        return false; // Exclude feature-related APIs
                    }

                    if (description.RelativePath != null &&
                        (description.RelativePath.Contains("/setting") ||
                         description.RelativePath.Contains("/settings")))
                    {
                        return false; // Exclude feature-related APIs
                    }

                    // Check if the relative path contains identity-related endpoints
                    if (description.RelativePath != null &&
                        (description.RelativePath.Contains("/identity") ||
                         description.RelativePath.Contains("/identities") ||
                         description.RelativePath.Contains("/users") ||
                         description.RelativePath.Contains("/roles")))
                    {
                        return false; // Exclude identity-related APIs
                    }

                    // Check if the API is from the AbpApiDefinition controller
                    if (description.ActionDescriptor != null &&
                        description.ActionDescriptor.DisplayName != null &&
                        description.ActionDescriptor.DisplayName.Contains("AbpApiDefinition"))
                    {
                        return false; // Exclude AbpApiDefinition API
                    }

                    // Check if the API is from the AbpApplicationConfiguration controller
                    //    if (description.ActionDescriptor != null &&
                    //        description.ActionDescriptor.DisplayName != null &&
                    //        description.ActionDescriptor.DisplayName.Contains("AbpApplicationConfiguration"))
                    //    {
                    //        return false; // Exclude AbpApplicationConfiguration API
                    //    }

                    // Check if the API is from the AbpApplicationLocalization controller
                    if (description.ActionDescriptor != null &&
                        description.ActionDescriptor.DisplayName != null &&
                        description.ActionDescriptor.DisplayName.Contains("AbpApplicationLocalization"))
                    {
                        return false; // Exclude AbpApplicationLocalization API
                    }

                    // Check if the relative path contains application-configuration
                    if (description.RelativePath != null &&
                        (description.RelativePath.Contains("api-definition") ||
                         description.RelativePath.Contains("api/abp/api-definition")))
                    {
                        return false; // Exclude api-definition API
                    }

                    // Check if the relative path contains application-configuration
                    //    if (description.RelativePath != null &&
                    //        (description.RelativePath.Contains("application-configuration") ||
                    //         description.RelativePath.Contains("api/abp/application-configuration")))
                    //    {
                    //        return false; // Exclude application-configuration API
                    //    }

                    // Check if the relative path contains application-localization
                    if (description.RelativePath != null &&
                        (description.RelativePath.Contains("application-localization") ||
                         description.RelativePath.Contains("api/abp/application-localization")))
                    {
                        return false; // Exclude application-localization API
                    }

                    return true; // Include all other APIs
                });

                options.CustomSchemaIds(type =>
                {
                    // Handle generic types
                    if (type.IsGenericType)
                    {
                        var prefix = type.Name.Split('`')[0];
                        var genericArgs = string.Join("And", type.GetGenericArguments().Select(t =>
                        {
                            if (t.IsGenericType)
                            {
                                var nestedPrefix = t.Name.Split('`')[0];
                                var nestedArgs = string.Join("And", t.GetGenericArguments().Select(nt => nt.Name));
                                return $"{nestedPrefix}Of{nestedArgs}";
                            }

                            return t.Name;
                        }));
                        return $"{prefix}Of{genericArgs}";
                    }

                    // Handle non-generic types
                    return type.Name;
                });
            }
        );
    }


    public override void OnApplicationInitialization(ApplicationInitializationContext context)
    {
        var app = context.GetApplicationBuilder();
        var env = context.GetEnvironment();

        app.UseForwardedHeaders();

        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }

        app.UseAbpRequestLocalization();

        if (!env.IsDevelopment())
        {
            app.UseErrorPage();
            app.UseHsts();
        }

        app.UseCorrelationId();
        app.MapAbpStaticAssets();
        app.UseAbpStudioLink();
        app.UseRouting();
        app.UseAbpSecurityHeaders();
        app.UseSession(); // Required for deferred user synchronization
        app.UseAuthentication();

        // Add user synchronization middleware after authentication
        app.UseMiddleware<Imip.JettyApproval.Web.Middleware.UserSynchronizationMiddleware>();

        // Add automatic SSO redirect middleware (temporarily disabled for debugging)
        // This will be re-enabled once the correlation issue is resolved
        /*
        app.Use(async (context, next) =>
        {
            // Skip automatic redirect for certain paths
            var path = context.Request.Path.Value?.ToLower();
            var skipPaths = new[] { "/signin-oidc", "/signout-callback-oidc", "/api/", "/swagger", "/health", "/account/externallogin", "/account/externallogincallback" };

            if (skipPaths.Any(skipPath => path?.StartsWith(skipPath) == true))
            {
                await next();
                return;
            }

            // If user is not authenticated and this is not an AJAX request, redirect to OIDC
            if (!context.User.Identity?.IsAuthenticated == true &&
                !context.Request.Headers.ContainsKey("X-Requested-With") &&
                context.Request.Method == "GET" &&
                !path?.Contains(".") == true) // Skip static files
            {
                // Store the original URL for return after authentication
                var returnUrl = context.Request.Path + context.Request.QueryString;
                var challengeUrl = $"/Account/ExternalLogin?provider=oidc&returnUrl={Uri.EscapeDataString(returnUrl)}";
                context.Response.Redirect(challengeUrl);
                return;
            }

            await next();
        });
        */

        if (MultiTenancyConsts.IsEnabled)
        {
            app.UseMultiTenancy();
        }

        app.UseUnitOfWork();
        app.UseDynamicClaims();
        app.UseAuthorization();
        app.UseInertia();
        app.UseSwagger();
        app.UseAbpSwaggerUI(options =>
        {
            options.SwaggerEndpoint("/swagger/v1/swagger.json", "JettyApproval API");
        });
        app.UseAuditing();
        app.UseAbpSerilogEnrichers();
        app.UseConfiguredEndpoints();
        app.UseConfiguredEndpoints(endpoints =>
        {
            endpoints.MapControllerRoute(
                name: "default",
                pattern: "{controller=Admin}/{action=Index}/{id?}");
        });
    }
}
